# Dependencies
node_modules
npm-debug.log*

# Runtime data
uploads/*
uploads_history/*
*.log
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# macOS
.DS_Store

# Windows
Thumbs.db

# IDE files
.vscode
.idea
*.swp
*.swo

# Git
.git
.gitignore

# Docker
Dockerfile*
docker-compose*
.dockerignore

# Documentation (optional, remove if you want docs in container)
*.md
!CHANGELOG.md

# Temporary files
*.tmp
*.temp