class FileUploader {
  constructor() {
    this.uploadedFiles = {};
    this.currentUploadArea = null;
    this.ws = null;
    this.init();
    this.setupWebSocket();
  }

  init() {
    this.setupEventListeners();
    this.setupTabSwitching();
    this.loadChangelog();
  }

  setupWebSocket() {
    const protocol = window.location.protocol === "https:" ? "wss:" : "ws:";
    const wsUrl = `${protocol}//${window.location.host}`;

    this.ws = new WebSocket(wsUrl);

    this.ws.onopen = () => {
      console.log("WebSocket connected");
    };

    this.ws.onmessage = (event) => {
      const data = JSON.parse(event.data);
      if (data.type === "progress") {
        this.updateProgress(data);
      }
    };

    this.ws.onclose = () => {
      console.log("WebSocket disconnected");
      // Try to reconnect after 3 seconds
      setTimeout(() => this.setupWebSocket(), 3000);
    };

    this.ws.onerror = (error) => {
      console.error("WebSocket error:", error);
    };
  }

  updateProgress(data) {
    const generateBtn = document.getElementById("generateBtn");
    const progressText = `${data.message}`;

    if (generateBtn.textContent.includes("Обработка файлов")) {
      generateBtn.textContent = progressText;
    }

    // Update progress in console for debugging
    console.log(`Progress: ${data.progress}% (${data.current}/${data.total})`);
  }

  setupEventListeners() {
    const uploadAreas = document.querySelectorAll(".upload-area");
    const fileInput = document.getElementById("fileInput");
    const generateBtn = document.getElementById("generateBtn");
    const generateProjectsBtn = document.getElementById("generateProjectsBtn");
    const generateTerminationsBtn = document.getElementById("generateTerminationsBtn");

    uploadAreas.forEach((area) => {
      area.addEventListener("click", () => this.handleAreaClick(area));
      area.addEventListener("dragover", (e) => this.handleDragOver(e, area));
      area.addEventListener("dragleave", (e) => this.handleDragLeave(e, area));
      area.addEventListener("drop", (e) => this.handleDrop(e, area));
    });

    fileInput.addEventListener("change", (e) => this.handleFileSelect(e));
    generateBtn.addEventListener("click", () => this.generateFile());
    if (generateProjectsBtn) {
      generateProjectsBtn.addEventListener("click", () => this.generateProjects());
    }
    if (generateTerminationsBtn) {
      generateTerminationsBtn.addEventListener("click", () => this.generateTerminations());
    }
  }

  setupTabSwitching() {
    const menuButtons = document.querySelectorAll(".menu-button");
    
    menuButtons.forEach((button) => {
      button.addEventListener("click", (e) => {
        const targetTab = e.currentTarget.dataset.tab;
        this.switchTab(targetTab);
      });
    });
  }

  switchTab(targetTab) {
    // Remove active class from all menu buttons
    const menuButtons = document.querySelectorAll(".menu-button");
    menuButtons.forEach((button) => {
      button.classList.remove("active");
    });

    // Remove active class from all tab contents with fade out
    const tabContents = document.querySelectorAll(".tab-content");
    tabContents.forEach((content) => {
      content.classList.remove("active", "fade-in");
    });

    // Add active class to clicked menu button
    const activeButton = document.querySelector(`[data-tab="${targetTab}"]`);
    if (activeButton) {
      activeButton.classList.add("active");
    }

    // Add active class and animation to corresponding tab content
    const activeContent = document.getElementById(targetTab);
    if (activeContent) {
      // Small delay to ensure the display change happens first
      setTimeout(() => {
        activeContent.classList.add("active", "fade-in");
      }, 10);
    }
  }

  handleAreaClick(area) {
    this.currentUploadArea = area;
    document.getElementById("fileInput").click();
  }

  handleDragOver(e, area) {
    e.preventDefault();
    area.classList.add("dragover");
  }

  handleDragLeave(e, area) {
    e.preventDefault();
    area.classList.remove("dragover");
  }

  handleDrop(e, area) {
    e.preventDefault();
    area.classList.remove("dragover");
    this.currentUploadArea = area;

    const files = e.dataTransfer.files;
    if (files.length > 0) {
      this.processFile(files[0]);
    }
  }

  handleFileSelect(e) {
    const files = e.target.files;
    if (files.length > 0 && this.currentUploadArea) {
      this.processFile(files[0]);
    }
  }

  processFile(file) {
    if (!this.validateFile(file)) {
      return;
    }

    const fileType = this.currentUploadArea.dataset.fileType;
    this.uploadFile(file, fileType);
  }

  validateFile(file) {
    const area = this.currentUploadArea;

    if (!file.name.toLowerCase().endsWith(".xlsx")) {
      this.showFileError(area, "Пожалуйста, выберите файл формата .xlsx");
      return false;
    }

    area.classList.remove("error");
    return true;
  }

  showFileError(area, message) {
    area.classList.add("error");
    area.querySelector(".upload-text").textContent = message;

    setTimeout(() => {
      area.classList.remove("error");
      area.querySelector(".upload-text").textContent =
        "Перетащите файл .xlsx сюда или нажмите для выбора";
    }, 3000);
  }

  uploadFile(file, fileType) {
    const area = this.currentUploadArea;
    const progressBar = area.querySelector(".progress-bar");
    const progressFill = area.querySelector(".progress-fill");
    const fileInfo = area.querySelector(".file-info");

    progressBar.style.display = "block";
    progressFill.style.width = "0%";

    const formData = new FormData();
    formData.append("file", file);
    formData.append("fileType", fileType);

    const xhr = new XMLHttpRequest();

    xhr.upload.addEventListener("progress", (e) => {
      if (e.lengthComputable) {
        const percentComplete = (e.loaded / e.total) * 100;
        progressFill.style.width = percentComplete + "%";
      }
    });

    xhr.addEventListener("load", () => {
      if (xhr.status === 200) {
        const response = JSON.parse(xhr.responseText);
        if (response.success) {
          this.onUploadSuccess(area, file, fileType);
        } else {
          this.onUploadError(area, response.error);
        }
      } else {
        this.onUploadError(area, "Ошибка загрузки файла");
      }
    });

    xhr.addEventListener("error", () => {
      this.onUploadError(area, "Ошибка соединения");
    });

    xhr.open("POST", "/upload");
    xhr.send(formData);
  }

  onUploadSuccess(area, file, fileType) {
    area.classList.add("uploaded");
    area.querySelector(".upload-text").textContent = "Файл успешно загружен";
    area.querySelector(".file-info").textContent = `${
      file.name
    } (${this.formatFileSize(file.size)})`;

    this.uploadedFiles[fileType] = true;
    this.checkAllFilesUploaded();
  }

  onUploadError(area, errorMessage) {
    area.classList.add("error");
    area.querySelector(".upload-text").textContent = errorMessage;
    area.querySelector(".progress-bar").style.display = "none";

    setTimeout(() => {
      area.classList.remove("error");
      area.querySelector(".upload-text").textContent =
        "Перетащите файл .xlsx сюда или нажмите для выбора";
    }, 3000);
  }

  checkAllFilesUploaded() {
    const requiredFiles = [
      "esup_fsds",
      "esup_completeness",
      "pf_all_timers",
      "columns_control",
    ];
    const allUploaded = requiredFiles.every(
      (fileType) => this.uploadedFiles[fileType]
    );

    const generateBtn = document.getElementById("generateBtn");
    generateBtn.disabled = !allUploaded;
  }

  generateFile() {
    const generateBtn = document.getElementById("generateBtn");
    const errorMessage = document.getElementById("errorMessage");
    const successMessage = document.getElementById("successMessage");

    generateBtn.disabled = true;
    generateBtn.textContent = "Обработка файлов...";
    errorMessage.style.display = "none";
    successMessage.style.display = "none";

    fetch("/generate", {
      method: "POST",
    })
      .then((response) => {
        const contentType = response.headers.get("Content-Type");
        console.log("Response content type:", contentType);

        if (contentType && contentType.includes("application/json")) {
          return response.json().then((data) => ({ json: data }));
        } else if (contentType && contentType.includes("spreadsheetml.sheet")) {
          return response.blob().then((blob) => ({ blob }));
        } else {
          // Try to parse as JSON first, then as blob
          return response.text().then((text) => {
            try {
              return { json: JSON.parse(text) };
            } catch {
              // If not JSON, treat as blob
              return response.blob().then((blob) => ({ blob }));
            }
          });
        }
      })
      .then((data) => {
        if (data.blob) {
          console.log("Blob size:", data.blob.size);
          if (data.blob.size < 1000) {
            throw new Error("Файл слишком мал, возможно поврежден");
          }
          this.downloadFile(data.blob, "import_timers_update.xlsx");
          successMessage.textContent =
            "Файл импорта успешно сгенерирован и загружен";
          successMessage.style.display = "block";
        } else if (data.json && data.json.error) {
          errorMessage.textContent = data.json.error;
          errorMessage.style.display = "block";
        } else {
          errorMessage.textContent = "Неизвестная ошибка при генерации файла";
          errorMessage.style.display = "block";
        }
      })
      .catch((error) => {
        console.error("Generate error:", error);
        errorMessage.textContent =
          "Произошла ошибка при генерации файла: " + error.message;
        errorMessage.style.display = "block";
      })
      .finally(() => {
        generateBtn.textContent = "Сгенерировать файл импорта";
        this.checkAllFilesUploaded();
      });
  }

  generateProjects() {
    // Placeholder functionality for the "Создание проектов" tab
    const errorMessage = document.getElementById("errorMessageProjects");
    errorMessage.textContent = "Функция создания проектов находится в разработке";
    errorMessage.style.display = "block";
    
    setTimeout(() => {
      errorMessage.style.display = "none";
    }, 3000);
  }

  generateTerminations() {
    // Placeholder functionality for the "Импорт расторжений" tab
    const errorMessage = document.getElementById("errorMessageTerminations");
    errorMessage.textContent = "Функция импорта расторжений находится в разработке";
    errorMessage.style.display = "block";
    
    setTimeout(() => {
      errorMessage.style.display = "none";
    }, 3000);
  }

  downloadFile(blob, filename) {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    // Reset all file placeholders after download
    this.resetAllPlaceholders();
  }

  resetAllPlaceholders() {
    // Clear uploaded files tracking
    this.uploadedFiles = {};

    // Reset all upload areas
    const uploadAreas = document.querySelectorAll(".upload-area");
    uploadAreas.forEach((area) => {
      // Remove all state classes
      area.classList.remove("uploaded", "error", "dragover");

      // Reset text content
      area.querySelector(".upload-text").textContent =
        "Перетащите файл .xlsx сюда или нажмите для выбора";

      // Clear file info
      const fileInfo = area.querySelector(".file-info");
      if (fileInfo) {
        fileInfo.textContent = "";
      }

      // Hide progress bar
      const progressBar = area.querySelector(".progress-bar");
      if (progressBar) {
        progressBar.style.display = "none";
      }

      // Reset progress fill
      const progressFill = area.querySelector(".progress-fill");
      if (progressFill) {
        progressFill.style.width = "0%";
      }
    });

    // Reset generate button state
    const generateBtn = document.getElementById("generateBtn");
    generateBtn.disabled = true;
    generateBtn.textContent = "Сгенерировать файл импорта";

    // Clear file input
    const fileInput = document.getElementById("fileInput");
    fileInput.value = "";

    // Ensure button state is properly updated based on uploaded files
    this.checkAllFilesUploaded();
  }

  formatFileSize(bytes) {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  }

  async loadChangelog() {
    try {
      const response = await fetch("/changelog");
      const data = await response.json();
      
      if (data.success) {
        const tooltip = document.getElementById("versionTooltip");
        if (tooltip) {
          // Convert markdown to basic HTML for display
          const htmlContent = data.content
            .replace(/# /g, "")
            .replace(/## /g, "")
            .replace(/\n\n/g, "<br><br>")
            .replace(/\n/g, "<br>");
          tooltip.innerHTML = htmlContent;
        }
      } else {
        console.error("Failed to load changelog:", data.error);
      }
    } catch (error) {
      console.error("Error loading changelog:", error);
      const tooltip = document.getElementById("versionTooltip");
      if (tooltip) {
        tooltip.innerHTML = "v 1.0 - 2025-07-22<br>Первая прод-версия генератора отчетов из ЕСУП для импорта в Планфикс";
      }
    }
  }
}

document.addEventListener("DOMContentLoaded", () => {
  new FileUploader();
});
