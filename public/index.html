<!DOCTYPE html>
<html lang="ru">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Генератор файла импорта регулярных апдейтов таймеров ПФ</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 0;
        margin: 0;
      }

      .container {
        width: 100vw;
        height: 100vh;
        margin: 0;
        background: white;
        border-radius: 0;
        box-shadow: none;
        padding: 0;
        position: relative;
        overflow: hidden;
      }

      h1 {
        text-align: center;
        color: #333;
        margin-bottom: 60px;
        font-size: 2.5rem;
        font-weight: 300;
      }

      .title-break {
        display: block;
      }

      @media (max-width: 1200px) {
        h1 {
          font-size: 2rem;
        }

        .title-break {
          display: none;
        }
      }

      @media (max-width: 768px) {
        h1 {
          font-size: 1.5rem;
          word-break: keep-all;
          overflow-wrap: break-word;
        }

        .title-break {
          display: none;
        }
      }

      .upload-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 24px;
        margin-bottom: 60px;
      }

      .upload-area {
        border: 3px dashed #ddd;
        border-radius: 12px;
        padding: 20px 15px;
        text-align: center;
        transition: all 0.3s ease;
        cursor: pointer;
        background: #fafafa;
        position: relative;
        min-height: 180px;
        display: flex;
        flex-direction: column;
        justify-content: center;
      }

      .upload-area:hover {
        border-color: #667eea;
        background: #f0f4ff;
      }

      .upload-area.dragover {
        border-color: #667eea;
        background: #e8f2ff;
        transform: scale(1.02);
      }

      .upload-area.uploaded {
        border-color: #4caf50;
        background: #f1f8e9;
      }

      .upload-area.error {
        border-color: #f44336;
        background: #ffebee;
      }

      .upload-area[data-file-type="esup_fsds"],
      .upload-area[data-file-type="esup_completeness"],
      .upload-area[data-file-type="pf_all_timers"],
      .upload-area[data-file-type="esup_fsds_projects"],
      .upload-area[data-file-type="esup_fsds_basic_projects"],
      .upload-area[data-file-type="pf_all_timers_projects"],
      .upload-area[data-file-type="esup_terminations"],
      .upload-area[data-file-type="pf_all_timers_terminations"] {
        background: #f0f8ff;
      }

      .upload-area[data-file-type="columns_control"],
      .upload-area[data-file-type="columns_control_projects"],
      .upload-area[data-file-type="columns_control_terminations"] {
        background: #fffdf0;
      }

      .upload-title {
        font-size: 1.1rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 12px;
      }

      .upload-text {
        color: #666;
        font-size: 0.9rem;
        margin-bottom: 15px;
      }

      .progress-bar {
        width: 100%;
        height: 8px;
        background: #eee;
        border-radius: 4px;
        overflow: hidden;
        margin-top: 15px;
        display: none;
      }

      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #667eea, #764ba2);
        width: 0%;
        transition: width 0.3s ease;
      }

      .file-info {
        margin-top: 10px;
        font-size: 0.8rem;
        color: #666;
      }

      .generate-btn {
        display: block;
        width: 300px;
        margin: 40px auto;
        padding: 18px 36px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border: none;
        border-radius: 8px;
        font-size: 1.1rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        opacity: 0.5;
        pointer-events: none;
      }

      .generate-btn:not(:disabled) {
        opacity: 1;
        pointer-events: auto;
      }

      .generate-btn:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
      }

      .error-message {
        background: #ffebee;
        border: 1px solid #f44336;
        color: #c62828;
        padding: 20px;
        border-radius: 8px;
        margin-top: 30px;
        display: none;
      }

      .success-message {
        background: #f1f8e9;
        border: 1px solid #4caf50;
        color: #2e7d32;
        padding: 20px;
        border-radius: 8px;
        margin-top: 30px;
        display: none;
      }

      .hidden-input {
        display: none;
      }

      .upload-icon {
        font-size: 2rem;
        margin-bottom: 10px;
        opacity: 0.5;
      }

      @media (max-width: 1024px) {
        .upload-grid {
          grid-template-columns: repeat(2, 1fr);
        }
      }

      @media (max-width: 640px) {
        .upload-grid {
          grid-template-columns: 1fr;
        }
      }

      .version {
        position: absolute;
        bottom: 20px;
        right: 20px;
        color: #999;
        font-size: 0.8rem;
        font-weight: 500;
        cursor: pointer;
      }

      .version-tooltip {
        position: absolute;
        bottom: 100%;
        right: 0;
        background: #333;
        color: white;
        padding: 10px 15px;
        border-radius: 6px;
        font-size: 0.75rem;
        white-space: nowrap;
        opacity: 0;
        visibility: hidden;
        transform: translateY(10px);
        transition: all 0.3s ease;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1000;
      }

      .version-tooltip::after {
        content: '';
        position: absolute;
        top: 100%;
        right: 20px;
        border: 5px solid transparent;
        border-top-color: #333;
      }

      .version:hover .version-tooltip {
        opacity: 1;
        visibility: visible;
        transform: translateY(0);
      }

      /* Layout styles */
      .main-layout {
        display: flex;
        gap: 0;
        height: 100vh;
        min-height: 100vh;
        margin: 0;
      }

      .menu-area {
        width: 350px;
        background: #2c3e50;
        border-radius: 0;
        padding: 20px;
        flex-shrink: 0;
        box-shadow: none;
      }

      .content-area {
        flex: 1;
        display: flex;
        flex-direction: column;
        min-width: 0;
        padding: 30px;
        overflow-y: auto;
      }

      .menu-button {
        width: 100%;
        padding: 16px 20px;
        background: transparent;
        border: none;
        border-radius: 8px;
        cursor: pointer;
        font-size: 1rem;
        font-weight: 500;
        color: #bdc3c7;
        transition: all 0.3s ease;
        text-align: left;
        margin-bottom: 8px;
        display: flex;
        align-items: center;
        gap: 12px;
        line-height: 1.4;
      }

      .menu-button.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        font-weight: 500;
      }

      .menu-button:hover:not(.active) {
        color: #ecf0f1;
        background: rgba(255, 255, 255, 0.1);
      }

      .menu-icon {
        font-size: 1.2rem;
        width: 20px;
        text-align: center;
      }

      .tab-content {
        display: none;
        flex: 1;
        overflow-y: auto;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.4s ease;
      }

      .tab-content.active {
        display: flex;
        flex-direction: column;
        opacity: 1;
        transform: translateY(0);
      }

      .tab-content.fade-in {
        animation: fadeInUp 0.4s ease forwards;
      }

      @keyframes fadeInUp {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Initial page load animations */
      .menu-area {
        opacity: 0;
        transform: translateX(-50px);
        animation: slideInFromLeft 0.6s ease 0.2s forwards;
      }

      .content-area {
        opacity: 0;
        transform: translateX(30px);
        animation: slideInFromRight 0.6s ease 0.4s forwards;
      }

      @keyframes slideInFromLeft {
        from {
          opacity: 0;
          transform: translateX(-50px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @keyframes slideInFromRight {
        from {
          opacity: 0;
          transform: translateX(30px);
        }
        to {
          opacity: 1;
          transform: translateX(0);
        }
      }

      @media (max-width: 1024px) {
        .main-layout {
          flex-direction: column;
          height: auto;
          gap: 20px;
        }

        .menu-area {
          width: 100%;
          padding: 15px;
        }

        .menu-button {
          padding: 12px 16px;
          font-size: 0.95rem;
        }
      }

      @media (max-width: 640px) {
        .menu-button {
          padding: 10px 12px;
          font-size: 0.9rem;
          gap: 8px;
        }

        .menu-icon {
          font-size: 1rem;
        }
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="main-layout">
        <div class="menu-area">
          <button class="menu-button active" data-tab="import-updates">
            Генератор импорта для апдейта текущих таймеров ПФ
          </button>
          <button class="menu-button" data-tab="create-projects">
            Генератор импорта для создания новых таймеров ПФ
          </button>
          <button class="menu-button" data-tab="import-terminations">
            Генератор импорта расторжений
          </button>
        </div>

        <div class="content-area">
          <div class="tab-content active" id="import-updates">
        <h1>
          Генератор файла импорта<br class="title-break" />
          регулярных апдейтов таймеров ПФ
        </h1>
      <div class="upload-grid">
        <div class="upload-area" data-file-type="esup_fsds">
          <div class="upload-icon">📄</div>
          <div class="upload-title">Отчет ЕСУП ФСДС</div>
          <div class="upload-text">
            Перетащите файл .xlsx сюда или нажмите для выбора
          </div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="file-info"></div>
        </div>

        <div class="upload-area" data-file-type="esup_completeness">
          <div class="upload-icon">📄</div>
          <div class="upload-title">Отчет ЕСУП комплектность</div>
          <div class="upload-text">
            Перетащите файл .xlsx сюда или нажмите для выбора
          </div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="file-info"></div>
        </div>

        <div class="upload-area" data-file-type="pf_all_timers">
          <div class="upload-icon">📄</div>
          <div class="upload-title">Отчет по всем таймерам Планфикс</div>
          <div class="upload-text">
            Перетащите файл .xlsx сюда или нажмите для выбора
          </div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="file-info"></div>
        </div>

        <div class="upload-area" data-file-type="columns_control">
          <div class="upload-icon">📄</div>
          <div class="upload-title">Контроль и конвертация заголовков</div>
          <div class="upload-text">
            Перетащите файл .xlsx сюда или нажмите для выбора
          </div>
          <div class="progress-bar">
            <div class="progress-fill"></div>
          </div>
          <div class="file-info"></div>
        </div>
      </div>

      <button class="generate-btn" id="generateBtn" disabled>
        Сгенерировать файл импорта апдейта таймеров
      </button>

            <div class="error-message" id="errorMessage"></div>
            <div class="success-message" id="successMessage"></div>
          </div>

          <div class="tab-content" id="create-projects">
        <h1>
          Генератор файла импорта<br class="title-break" />
          новых проектов ПФ
        </h1>
        
        <div class="upload-grid">
          <div class="upload-area" data-file-type="esup_fsds_projects">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Отчет ЕСУП ФСДС</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>

          <div class="upload-area" data-file-type="esup_fsds_basic_projects">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Отчет ЕСУП ФСДС базовый</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>

          <div class="upload-area" data-file-type="pf_all_timers_projects">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Отчет по всем таймерам Планфикс</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>

          <div class="upload-area" data-file-type="columns_control_projects">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Контроль и конвертация заголовков</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>
        </div>

        <button class="generate-btn" id="generateProjectsBtn" disabled>
          Сгенерировать файл импорта новых проектов
        </button>

            <div class="error-message" id="errorMessageProjects"></div>
            <div class="success-message" id="successMessageProjects"></div>
          </div>

          <div class="tab-content" id="import-terminations">
        <h1>
          Генератор файла импорта<br class="title-break" />
          расторжений ПФ
        </h1>
        
        <div class="upload-grid">
          <div class="upload-area" data-file-type="esup_terminations">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Отчет ЕСУП расторжения</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>

          <div class="upload-area" data-file-type="pf_all_timers_terminations">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Отчет по всем таймерам Планфикс</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>

          <div class="upload-area" data-file-type="columns_control_terminations">
            <div class="upload-icon">📄</div>
            <div class="upload-title">Контроль и конвертация заголовков</div>
            <div class="upload-text">
              Перетащите файл .xlsx сюда или нажмите для выбора
            </div>
            <div class="progress-bar">
              <div class="progress-fill"></div>
            </div>
            <div class="file-info"></div>
          </div>
        </div>

        <button class="generate-btn" id="generateTerminationsBtn" disabled>
          Сгенерировать файл импорта расторжений
        </button>

            <div class="error-message" id="errorMessageTerminations"></div>
            <div class="success-message" id="successMessageTerminations"></div>
          </div>
        </div>
      </div>
      
      <div class="version">
        Russtroy Import Generator v 1.2
        <div class="version-tooltip" id="versionTooltip">
          Loading changelog...
        </div>
      </div>
    </div>

    <input type="file" class="hidden-input" id="fileInput" accept=".xlsx" />

    <script src="script.js"></script>
  </body>
</html>
