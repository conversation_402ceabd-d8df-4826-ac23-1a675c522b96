# Docker Setup Guide

## Prerequisites
- Docker and Docker Compose installed on your system

## Quick Start

### Production Deployment
```bash
# Build and start the application
docker-compose up -d

# View logs
docker-compose logs -f

# Stop the application
docker-compose down
```

### Development Mode
```bash
# Start in development mode with live reload
docker-compose --profile dev up esup-converter-dev

# Or build and run development container
docker-compose up esup-converter-dev
```

## Manual Docker Commands

### Build Image
```bash
docker build -t esup-converter .
```

### Run Container
```bash
docker run -d \
  --name esup-converter \
  -p 3000:3000 \
  -v uploads_data:/app/uploads \
  -v uploads_history_data:/app/uploads_history \
  esup-converter
```

## Environment Variables
- `NODE_ENV`: Environment mode (development/production)
- `PORT`: Server port (default: 3000)
- `UPLOADS_DIR`: Upload directory path (default: uploads)
- `UPLOADS_HISTORY_DIR`: History directory path (default: uploads_history)
- `OUTPUT_FILENAME`: Generated file name (default: import_timers_update.xlsx)

## Volumes
- `uploads_data`: Persistent storage for uploaded files
- `uploads_history_data`: Persistent storage for processed files history
- `./file_references:/app/file_references:ro`: Reference files (read-only)

## Health Check
The container includes a health check that verifies the application is responding on port 3000.

## Accessing the Application
- Web interface: http://localhost:3000
- WebSocket endpoint: ws://localhost:3000

## File Persistence
Upload and history directories are mounted as Docker volumes to ensure data persistence across container restarts.

## Troubleshooting
```bash
# Check container status
docker-compose ps

# View application logs
docker-compose logs esup-converter

# Execute commands in running container
docker-compose exec esup-converter sh

# Restart the service
docker-compose restart esup-converter
```