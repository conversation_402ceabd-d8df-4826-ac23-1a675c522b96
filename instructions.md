- build simple web application that uploads 4 files and then downloads the result
- the app is very simple don’t overcomplicate it
- after reading all the input, make the plan and walk me through it, then start implementing step by step, ask for my verification and move on after I approve it
- the stack is
    - simple html with minimum javascript on the frontend
    - stylish minimal design
    - nodejs on the backend
    - no database is required at this stage
    - just upload 4 files, then perform operations that are described below, then download resultin file
- frontend
    - single page application with 4 placeholders for file uploads, that are also droppable areas so user can drag and drop files in that area and upload starts automatically with progress bar showing % of each uploaded file. The only format allowed for upload is *.xlsx, if user is trying to choose file of different type, show warning inside placeholder prompting user to choose file of allowed format instead
    - 4 placeholders are titled as follows
        1. “Отчет ЕСУП ФСДС”
        2. “Отчет ЕСУП комплектность”
        3. “Отчет по всем таймерам Планфикс”
        4. “Контроль и конвертация заголовков”
    - after all 4 files are uploaded, button “Сгенерировать файл импорта” is activated
    - after that backend starts manipulations with the uploaded files and returns result as described in the backend sections below
        - if there are no errors on the backend, then backend sends file to download
        - if there are errors then backend sends text description about errors that should be displayed in frontend
        - 
- backend
    - after uploading files are put in the /upload folder and renamed as
        1. esup_fsds.xlsx
        2. esup_completeness.xlsx
        3. pf_all_timers.xlsx
        4. columns_control.xlsx
    - extract first row from the first sheet of the following workbooks: 1) esup_fsds.xlsx, 2) esup_completeness.xlsx, and 3) pf_all_timers.xlsx into arrays 1) esup_fsds_header_all, 2) esup_completeness_header_all, and 3) pf_all_timers_header_all respectively
    - columns_control.xlsx has 3 sheets, named “esup_fsds”, “esup_completeness” and “pf_all_timers”, each sheet has two columns: “source_column_title” and “target_column_title”.. if any of those sheets or columns in either of the sheets are missing there should be error message returned to frontend with the description of what’s missing, but if everything is present then move on the the next step
    - check that every column listed in the source_column_title is present in the corresponding wokbook, i.e. all column names listed in the source_column_title column on the esup_fsds sheet are present in the esup_fsds.xlsx workbook on the 1st sheet, and repeat that check for esup_completeness and pf_all_timers using the same logic
    - if any of listed column names are missing from the corresponding workbook, then collect all missing names from all three workbooks and send error message to the frontend with the listing of missing columns, only if all columns are present then move on to the next step
    - create new workbook and save it as import_timers_update.xlsx
    - name the first sheet as import
    - copy all columns titles from the target_column_titlse column from the pf_all_timers sheet of the columns_control.xlsx  and paste them into the first row of the import sheet as new columns
    - using the same logic, copy all column titles from the target_column_title column from the esup_fsds sheet of the columns_control.xlsx  and paste them into the first row of the import sheet next to the previously copied pf_all_timers columns
    - using the same logic, copy all column titles from the target_column_title column from the esup_completeness sheet of the columns_control.xlsx  and paste them into the first row of the import sheet next to the previously copied esup_fsds columns
    - lastly, add to the import sheet column named s“Таймер обновлен из отчетов ЕСУП”
    - now comes the data copying stage from all three files to one eggregate import sheet
    - first, copy from timers_all_pf.xlsx all rows to the new import sheet but only for columns copied from columns_control.xlsx workbook from pf_all_timers sheet
    - second, for every row, copied from timers_all_pf.xlsx find row from esup_fsds.xlsx with ID column equal tßo the current ID columns of timers_all_pf and copy only columns that are present in the import sheet and after copyiyng all columns by the same logic find row from esup_completeness.xlsx with the same ID and copy all columns from it to the import sheet but only those columns that are present, and lastly for the column named “Таймер обновлен из отчетов ЕСУП” set value “Да”
    - continue until all rows from the timers_all_pf.xlsx are copied
    - save import_timers_update.xlsx and report to the frontend with automatic downloading resulting import_timers_update.xlsx file


    2025-07-28 add skip empty fsds lines

    2025-07-29 add new projects import
    Okay, make these additions to the web interface. Let's add a top bar with two sections.
     The first section will be called **Импорт апдейтов таймеров**, and the second section will be called **Создание проектов**. The screen that appears after activating the first tab bar is the current screen with four placeholders -- do not make any changed to it. The main point is to add second tab with new functionality. The second tab bar should contain almost similiar view with four placeholders, that are are titled as follows:
        1. “Отчет ЕСУП ФСДС”
        2. “Отчет ЕСУП ФСДС базовый”
        3. “Отчет по всем таймерам Планфикс”
        4. “Контроль и конвертация заголовков”
    All Placeholders have similiar styling as on the first tab

    
    let's change tab layout to vertical zones with menu area on the left and content area with file placegolders and button on the right main window...  and put two current tabs as a menu items on the menu panel, switching content area according to active menu item

    ok make layout full width

    make menu zone more contrast to the main area

    make menu section stick to the main wrapper without spacing

    use playwright mcp to look at the current layout -- remove top gap in the menu section
    server already running on port 60002 just open playwright and research the problem and remove top gap in the menu section

    make height of the main area full screen

    remove bold font when active menu selection

    remove icons to the left of menu items

    update version to 1.2 in changelog and package

    Add a little animation to the sections, a slight fade-in with a slight move up when clicking the menu item for that section. Also add an animation for the initial page load to create a smooth entrance effect.

    use playwright mcp to analyze current dashboard style and add another menu item named "Генератор импорта расторжений". When clicked it should open another section with almost similiar functionality as the other two sections: 3 file upload placeholders that are are titled as follows:
        1. “Отчет ЕСУП расторжения”
        2. “Отчет по всем таймерам Планфикс”
        3. “Контроль и конвертация заголовков”
    All Placeholders have similiar styling as on the first tab: 1 and 2 with blue bacground and 3 in yellowish
