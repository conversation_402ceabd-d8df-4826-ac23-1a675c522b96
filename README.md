# ESUP to PlanFix Import Converter

A web application that processes and converts ESUP (ЕСУП) report files into a format suitable for PlanFix import. The application consolidates data from multiple Excel reports into a single import file.

## Features

- Upload 4 different XLSX report files through a web interface
- Drag and drop file upload with progress tracking
- Real-time processing feedback via WebSocket connection
- Column mapping and validation using control files
- Automatic data consolidation and export

## File Types Required

The application requires 4 specific Excel files:

1. **ЕСУП ФСДС Report** (`esup_fsds.xlsx`)
2. **ЕСУП Completeness Report** (`esup_completeness.xlsx`) 
3. **PlanFix All Timers Report** (`pf_all_timers.xlsx`)
4. **Column Control File** (`columns_control.xlsx`)

## Prerequisites

- Node.js (version 14 or higher)
- npm (Node Package Manager)

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd esup_to_pf_import_converter
```

2. Install dependencies:
```bash
npm install
```

## Usage

### Development Mode

Start the development server:
```bash
npm run dev
```

### Production Mode

Start the production server:
```bash
npm start
```

The application will be available at `http://localhost:3000` (or the port specified in your environment variables).

### Docker Support

The application includes Docker support. See `DOCKER.md` for containerization instructions.

### Production Deployment with PM2

For production deployment with PM2:
```bash
npm run pm2startRusstroyEsupSyncProd
```

## How It Works

1. **Upload Phase**: Upload all 4 required Excel files through the web interface
2. **Validation Phase**: The system validates column mappings and data integrity
3. **Processing Phase**: Data is consolidated from all source files based on ID matching
4. **Export Phase**: A new `import_timers_update.xlsx` file is generated and automatically downloaded

## File Structure

```
├── server.js           # Main server application
├── public/            # Frontend files
│   ├── index.html     # Main web interface
│   └── script.js      # Client-side JavaScript
├── uploads/           # Temporary file storage
├── uploads_history/   # Historical uploads
├── file_references/   # Reference files
└── instructions.md    # Detailed technical specifications
```

## Configuration

Environment variables can be configured in a `.env` file:

- `PORT` - Server port (default: 3000)
- `UPLOADS_DIR` - Upload directory path (default: uploads)
- `HTTPS` - Enable HTTPS mode for production

## Dependencies

- **express** - Web framework
- **multer** - File upload handling
- **xlsx-js-style** - Excel file processing
- **ws** - WebSocket support for real-time updates
- **cors** - Cross-origin resource sharing
- **dotenv** - Environment variable management

## Error Handling

The application includes comprehensive error handling for:
- Missing or invalid file formats
- Column mapping validation
- Data integrity checks
- Missing required columns in source files

## Version History

See `CHANGELOG.md` for detailed version history and release notes.