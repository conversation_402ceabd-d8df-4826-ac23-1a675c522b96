# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Node.js web application that processes ESUP (ЕСУП) Excel reports and converts them into PlanFix import format. The application consolidates data from 4 different Excel files through a web interface and generates a single import file.

## Architecture

The application follows a simple client-server architecture:

- **Frontend**: Single HTML page (`public/index.html`) with vanilla JavaScript (`public/script.js`)
- **Backend**: Express.js server (`server.js`) with WebSocket support for real-time progress updates
- **File Processing**: Excel file manipulation using `xlsx-js-style` library
- **Upload Handling**: Multer middleware for file uploads with validation

### Key Components

- **FileUploader Class** (`public/script.js`): Handles drag-and-drop file uploads, progress tracking, and WebSocket communication
- **Main Server** (`server.js`): Express server with upload endpoints, file validation, and Excel processing logic
- **Column Control System**: Uses `columns_control.xlsx` to map source columns to target columns across different Excel files

## Common Commands

### Development
```bash
npm run dev          # Start development server
npm start           # Start production server
```

### Production Deployment
```bash
npm run pm2startRusstroyEsupSyncProd  # Deploy with PM2 in production mode with HTTPS
```

### Docker (see DOCKER.md)
```bash
docker build -t esup-converter .
docker-compose up
```

## File Processing Workflow

The application requires exactly 4 Excel files in this order:
1. **ЕСУП ФСДС Report** → renamed to `esup_fsds.xlsx`
2. **ЕСУП Completeness Report** → renamed to `esup_completeness.xlsx` 
3. **PlanFix All Timers Report** → renamed to `pf_all_timers.xlsx`
4. **Column Control File** → renamed to `columns_control.xlsx`

### Processing Steps
1. Upload validation and file renaming
2. Header extraction from first row of each Excel file
3. Column mapping validation using control file
4. Data consolidation based on ID matching
5. Export generation as `import_timers_update.xlsx`

## Important File Locations

- **Uploads**: `uploads/` (active), `uploads_history/` (archived with timestamps)
- **Reference Files**: `file_references/columns_control.xlsx` (template)
- **Frontend**: `public/index.html`, `public/script.js`
- **Configuration**: Environment variables in `.env` file

## WebSocket Communication

Real-time progress updates are sent via WebSocket from server to client during file processing. The client automatically reconnects on disconnection.

## Error Handling

The application validates:
- File format (only .xlsx allowed)
- Required Excel sheets and columns in control file
- Column mapping consistency between source files and control file
- Data integrity during processing

## Environment Variables

- `PORT`: Server port (default: 3000)
- `UPLOADS_DIR`: Upload directory path (default: uploads)
- `HTTPS`: Enable HTTPS mode for production

## Dependencies

Key dependencies include:
- `express`: Web framework
- `multer`: File upload handling  
- `xlsx-js-style`: Excel file processing with styling
- `ws`: WebSocket server
- `cors`: Cross-origin support