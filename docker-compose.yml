version: '3.8'

services:
  esup-converter:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - UPLOADS_DIR=uploads
      - UPLOADS_HISTORY_DIR=uploads_history
      - OUTPUT_FILENAME=import_timers_update.xlsx
    volumes:
      # Persist upload directories
      - uploads_data:/app/uploads
      - uploads_history_data:/app/uploads_history
      # Mount reference files (optional)
      - ./file_references:/app/file_references:ro
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Development service with live reload
  esup-converter-dev:
    build: 
      context: .
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - UPLOADS_DIR=uploads
      - UPLOADS_HISTORY_DIR=uploads_history
      - OUTPUT_FILENAME=import_timers_update.xlsx
    volumes:
      # Mount source code for development
      - .:/app
      - /app/node_modules
      # Persist upload directories
      - uploads_data:/app/uploads
      - uploads_history_data:/app/uploads_history
    restart: unless-stopped
    profiles: ["dev"]

volumes:
  uploads_data:
  uploads_history_data: