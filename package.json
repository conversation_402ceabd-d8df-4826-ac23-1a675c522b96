{"name": "app", "version": "1.2.0", "main": "index.js", "scripts": {"start": "node server.js", "dev": "node server.js", "test": "echo \"Error: no test specified\" && exit 1", "pm2startRusstroyEsupSyncProd": "HTTPS='true' pm2 start server.js --name russtroy-esup-sync-prod --log --time"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^5.1.0", "multer": "^2.0.2", "ws": "^8.18.3", "xlsx": "^0.18.5", "xlsx-js-style": "^1.2.0"}}