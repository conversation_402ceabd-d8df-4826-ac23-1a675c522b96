require("dotenv").config();

const express = require("express");
const multer = require("multer");
const path = require("path");
const fs = require("fs");
const XLSX = require("xlsx-js-style");
const cors = require("cors");
const http = require("http");
const WebSocket = require("ws");

const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });
const port = process.env.PORT || 3000;

// Store WebSocket connections
let wsClients = [];

// WebSocket connection handler
wss.on("connection", (ws) => {
  wsClients.push(ws);

  ws.on("close", () => {
    wsClients = wsClients.filter((client) => client !== ws);
  });
});

// Function to broadcast progress to all connected clients
function broadcastProgress(data) {
  const message = JSON.stringify(data);
  wsClients.forEach((client) => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

app.use(cors());
app.use(express.json());
app.use(express.static("public"));

const storage = multer.diskStorage({
  destination: (req, file, cb) => {
    const uploadsDir = path.join(
      __dirname,
      process.env.UPLOADS_DIR || "uploads"
    );
    if (!fs.existsSync(uploadsDir)) {
      fs.mkdirSync(uploadsDir, { recursive: true });
    }
    cb(null, uploadsDir);
  },
  filename: (req, file, cb) => {
    // Use original filename initially, renaming will happen in the endpoint
    cb(null, file.originalname);
  },
});

const upload = multer({
  storage: storage,
  fileFilter: (req, file, cb) => {
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.originalname.toLowerCase().endsWith(".xlsx")
    ) {
      cb(null, true);
    } else {
      cb(new Error("Только файлы .xlsx разрешены"), false);
    }
  },
});

class ExcelProcessor {
  constructor() {
    this.uploadsDir = path.join(
      __dirname,
      process.env.UPLOADS_DIR || "uploads"
    );
  }

  normalizeColumnName(columnName) {
    if (!columnName) return "";
    return columnName
      .toString()
      .replace(/\s+/g, " ") // Replace multiple spaces with single space
      .replace(/[\r\n\t]/g, "") // Remove line breaks and tabs
      .trim(); // Remove leading/trailing spaces
  }

  findColumnByNormalizedName(row, normalizedColumnName) {
    if (!normalizedColumnName) return undefined;

    // Find the original column name that matches the normalized name
    for (const [originalKey, value] of Object.entries(row)) {
      if (this.normalizeColumnName(originalKey) === normalizedColumnName) {
        return value;
      }
    }
    return undefined;
  }

  formatDateValue(value, columnType) {
    if (!columnType || !columnType.includes("date") || !value) {
      return value;
    }

    try {
      let date;
      if (typeof value === "number") {
        date = XLSX.SSF.parse_date_code(value);
        date = new Date(date.y, date.m - 1, date.d);
      } else if (typeof value === "string") {
        // Handle string dates more carefully to avoid day/month confusion
        const trimmedValue = value.toString().trim();
        
        // Try to parse Russian date formats first: DD.MM.YYYY or DD/MM/YYYY
        const russianDateMatch = trimmedValue.match(/^(\d{1,2})[./](\d{1,2})[./](\d{4})$/);
        if (russianDateMatch) {
          const day = parseInt(russianDateMatch[1], 10);
          const month = parseInt(russianDateMatch[2], 10);
          const year = parseInt(russianDateMatch[3], 10);
          
          // Validate date components
          if (day >= 1 && day <= 31 && month >= 1 && month <= 12 && year >= 1900) {
            date = new Date(year, month - 1, day);
          } else {
            // Fall back to default parsing if validation fails
            date = new Date(trimmedValue);
          }
        } else {
          // For other formats, use default parsing
          date = new Date(trimmedValue);
        }
      } else if (value instanceof Date) {
        date = value;
      } else {
        return value;
      }

      if (isNaN(date.getTime())) {
        return value;
      }

      return date.toLocaleDateString("ru-RU", {
        year: "numeric",
        month: "2-digit",
        day: "2-digit",
      });
    } catch (error) {
      return value;
    }
  }

  extractHeaders(filename) {
    try {
      const filePath = path.join(this.uploadsDir, filename);
      const workbook = XLSX.readFile(filePath);
      const firstSheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[firstSheetName];

      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
      return jsonData[0] || [];
    } catch (error) {
      throw new Error(
        `Ошибка чтения заголовков из ${filename}: ${error.message}`
      );
    }
  }

  validateColumnsControl() {
    try {
      // Find the columns_control file with any extension
      const files = fs.readdirSync(this.uploadsDir);
      const controlFile = files.find(
        (file) => path.parse(file).name === "columns_control"
      );
      if (!controlFile) {
        throw new Error("Файл columns_control не найден в папке uploads");
      }
      const filePath = path.join(this.uploadsDir, controlFile);
      const workbook = XLSX.readFile(filePath);

      const requiredSheets = [
        "esup_fsds",
        "esup_completeness",
        "pf_all_timers",
      ];
      const requiredColumns = ["source_column_title", "target_column_title"];

      for (const sheetName of requiredSheets) {
        if (!workbook.SheetNames.includes(sheetName)) {
          throw new Error(
            `Отсутствует лист "${sheetName}" в файле columns_control`
          );
        }

        const worksheet = workbook.Sheets[sheetName];
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
        const headers = jsonData[0] || [];

        for (const column of requiredColumns) {
          if (!headers.includes(column)) {
            throw new Error(
              `Отсутствует колонка "${column}" в листе "${sheetName}" файла columns_control`
            );
          }
        }
      }

      return true;
    } catch (error) {
      throw error;
    }
  }

  getControlColumns(sheetName) {
    // Find the columns_control file with any extension
    const files = fs.readdirSync(this.uploadsDir);
    const controlFile = files.find(
      (file) => path.parse(file).name === "columns_control"
    );
    if (!controlFile) {
      throw new Error("Файл columns_control не найден в папке uploads");
    }
    const filePath = path.join(this.uploadsDir, controlFile);
    const workbook = XLSX.readFile(filePath);
    const worksheet = workbook.Sheets[sheetName];
    const jsonData = XLSX.utils.sheet_to_json(worksheet);

    const result = {
      sourceColumns: jsonData
        .map((row) => row.source_column_title)
        .filter(Boolean),
      targetColumns: jsonData
        .map((row) => row.target_column_title)
        .filter(Boolean),
      columnTypes: jsonData
        .map((row) => row.type || "")
        .filter((type, index) => jsonData[index].target_column_title),
    };

    return result;
  }

  validateSourceColumns() {
    const errors = [];
    const fileBaseNames = ["esup_fsds", "esup_completeness", "pf_all_timers"];
    const files = fs.readdirSync(this.uploadsDir);

    for (const baseName of fileBaseNames) {
      try {
        // Find file by base name
        const matchedFile = files.find(
          (file) => path.parse(file).name === baseName
        );
        if (!matchedFile) {
          errors.push(`Файл ${baseName} не найден в папке uploads`);
          continue;
        }
        const headers = this.extractHeaders(matchedFile);
        const normalizedHeaders = headers.map((h) =>
          this.normalizeColumnName(h)
        );

        const { sourceColumns } = this.getControlColumns(baseName);
        const normalizedSourceColumns = sourceColumns.map((col) =>
          this.normalizeColumnName(col)
        );

        const missingColumns = normalizedSourceColumns.filter(
          (col) => col && !normalizedHeaders.includes(col)
        );

        if (missingColumns.length > 0) {
          errors.push(
            `В файле ${baseName} отсутствуют колонки: ${missingColumns.join(
              ", "
            )}`
          );
        }
      } catch (error) {
        errors.push(`Ошибка проверки файла ${baseName}: ${error.message}`);
      }
    }

    if (errors.length > 0) {
      throw new Error(errors.join("\n"));
    }
  }

  readExcelData(filename) {
    const filePath = path.join(this.uploadsDir, filename);
    const workbook = XLSX.readFile(filePath);
    const firstSheetName = workbook.SheetNames[0];
    const worksheet = workbook.Sheets[firstSheetName];
    const data = XLSX.utils.sheet_to_json(worksheet);

    return data;
  }

  createImportFile() {
    try {
      const newWorkbook = XLSX.utils.book_new();

      const { targetColumns: pfTargetColumns } =
        this.getControlColumns("pf_all_timers");
      const { targetColumns: esupFsdsTargetColumns } =
        this.getControlColumns("esup_fsds");
      const { targetColumns: esupCompletenessTargetColumns } =
        this.getControlColumns("esup_completeness");

      // Filter out empty/undefined columns
      const allHeaders = [
        ...pfTargetColumns.filter((col) => col && col.trim()),
        ...esupFsdsTargetColumns.filter((col) => col && col.trim()),
        ...esupCompletenessTargetColumns.filter((col) => col && col.trim()),
        "Таймер обновлен из отчетов ЕСУП",
      ];

      // Find files by their base names (files are already renamed during upload)
      const files = fs.readdirSync(this.uploadsDir);
      const pfFile = files.find(
        (file) => path.parse(file).name === "pf_all_timers"
      );
      const esupFsdsFile = files.find(
        (file) => path.parse(file).name === "esup_fsds"
      );
      const esupCompletenessFile = files.find(
        (file) => path.parse(file).name === "esup_completeness"
      );

      if (!pfFile || !esupFsdsFile || !esupCompletenessFile) {
        const missingFiles = [];
        if (!pfFile) missingFiles.push("pf_all_timers");
        if (!esupFsdsFile) missingFiles.push("esup_fsds");
        if (!esupCompletenessFile) missingFiles.push("esup_completeness");
        throw new Error(
          `Отсутствуют необходимые файлы: ${missingFiles.join(", ")}`
        );
      }

      const pfData = this.readExcelData(pfFile);
      const esupFsdsData = this.readExcelData(esupFsdsFile);
      const esupCompletenessData = this.readExcelData(esupCompletenessFile);

      // Create lookup maps for faster data retrieval (O(1) instead of O(n))
      const esupFsdsMap = new Map();
      const esupCompletenessMap = new Map();

      // Build ESUP FSDS lookup map
      esupFsdsData.forEach((row) => {
        const id =
          this.findColumnByNormalizedName(row, "ID мероприятия") ||
          this.findColumnByNormalizedName(row, "Id мероприятия") ||
          this.findColumnByNormalizedName(row, "ID") ||
          this.findColumnByNormalizedName(row, "id") ||
          this.findColumnByNormalizedName(row, "Id") ||
          row["ID мероприятия"] ||
          row["Id мероприятия"] ||
          row["ID"] ||
          row["id"] ||
          row["Id"];
        if (id) {
          const normalizedId = String(id).trim();
          esupFsdsMap.set(normalizedId, row);
        }
      });

      // Build ESUP Completeness lookup map
      esupCompletenessData.forEach((row) => {
        const id =
          this.findColumnByNormalizedName(row, "ID мероприятия") ||
          this.findColumnByNormalizedName(row, "Id мероприятия") ||
          this.findColumnByNormalizedName(row, "ID") ||
          this.findColumnByNormalizedName(row, "id") ||
          this.findColumnByNormalizedName(row, "Id") ||
          row["ID мероприятия"] ||
          row["Id мероприятия"] ||
          row["ID"] ||
          row["id"] ||
          row["Id"];
        if (id) {
          const normalizedId = String(id).trim();
          esupCompletenessMap.set(normalizedId, row);
        }
      });

      const pfControl = this.getControlColumns("pf_all_timers");
      const esupFsdsControl = this.getControlColumns("esup_fsds");
      const esupCompletenessControl =
        this.getControlColumns("esup_completeness");

      const importData = [];

      // If no data, create at least headers
      if (pfData.length === 0) {
        const emptyRow = {};
        allHeaders.forEach((header) => {
          emptyRow[header] = "";
        });
        importData.push(emptyRow);
      } else {
        // Notify frontend that processing started
        broadcastProgress({
          type: "progress",
          progress: 0,
          current: 0,
          total: pfData.length,
          message: `Starting to process ${pfData.length} rows...`,
        });

        for (let rowIndex = 0; rowIndex < pfData.length; rowIndex++) {
          const pfRow = pfData[rowIndex];

          // Show progress every 10% or every 100 rows, whichever is smaller
          const progressInterval = Math.min(
            100,
            Math.max(1, Math.floor(pfData.length / 10))
          );
          if (
            rowIndex % progressInterval === 0 ||
            rowIndex === pfData.length - 1
          ) {
            const progress = Math.round(((rowIndex + 1) / pfData.length) * 100);

            // Broadcast progress to frontend
            broadcastProgress({
              type: "progress",
              progress: progress,
              current: rowIndex + 1,
              total: pfData.length,
              message: `Processing row ${rowIndex + 1} of ${
                pfData.length
              } (${progress}%)`,
            });
          }

          const newRow = {};

          // Initialize all headers with empty values to ensure consistent structure
          allHeaders.forEach((header) => {
            newRow[header] = "";
          });

          // Copy PF data
          let pfFieldsCopied = 0;
          for (let i = 0; i < pfControl.sourceColumns.length; i++) {
            const sourceCol = pfControl.sourceColumns[i];
            const targetCol = pfControl.targetColumns[i];
            const columnType = pfControl.columnTypes[i];
            const normalizedSourceCol = this.normalizeColumnName(sourceCol);

            if (targetCol && targetCol.trim() && normalizedSourceCol) {
              const value = this.findColumnByNormalizedName(
                pfRow,
                normalizedSourceCol
              );
              if (value !== undefined) {
                const formattedValue = this.formatDateValue(value, columnType);
                newRow[targetCol] = formattedValue;
                pfFieldsCopied++;
              }
            }
          }

          // Find ID using normalized column matching - try Russian column names first
          const pfId =
            this.findColumnByNormalizedName(pfRow, "ID мероприятия") ||
            this.findColumnByNormalizedName(pfRow, "Id мероприятия") ||
            this.findColumnByNormalizedName(pfRow, "ID") ||
            this.findColumnByNormalizedName(pfRow, "id") ||
            this.findColumnByNormalizedName(pfRow, "Id") ||
            pfRow["ID мероприятия"] ||
            pfRow["Id мероприятия"] ||
            pfRow["ID"] ||
            pfRow["id"] ||
            pfRow["Id"];

          if (pfId) {
            const normalizedPfId = String(pfId).trim();
            const esupFsdsRow = esupFsdsMap.get(normalizedPfId);

            // Only process rows that have a matching ID in FSDS file
            if (esupFsdsRow) {
              let esupFsdsFieldsCopied = 0;
              for (let i = 0; i < esupFsdsControl.sourceColumns.length; i++) {
                const sourceCol = esupFsdsControl.sourceColumns[i];
                const targetCol = esupFsdsControl.targetColumns[i];
                const columnType = esupFsdsControl.columnTypes[i];
                const normalizedSourceCol = this.normalizeColumnName(sourceCol);

                if (targetCol && targetCol.trim() && normalizedSourceCol) {
                  const value = this.findColumnByNormalizedName(
                    esupFsdsRow,
                    normalizedSourceCol
                  );
                  if (value !== undefined) {
                    const formattedValue = this.formatDateValue(
                      value,
                      columnType
                    );
                    newRow[targetCol] = formattedValue;
                    esupFsdsFieldsCopied++;
                  }
                }
              }

              const esupCompletenessRow = esupCompletenessMap.get(normalizedPfId);

              if (esupCompletenessRow) {
                let esupCompletenessFieldsCopied = 0;
                for (
                  let i = 0;
                  i < esupCompletenessControl.sourceColumns.length;
                  i++
                ) {
                  const sourceCol = esupCompletenessControl.sourceColumns[i];
                  const targetCol = esupCompletenessControl.targetColumns[i];
                  const columnType = esupCompletenessControl.columnTypes[i];
                  const normalizedSourceCol = this.normalizeColumnName(sourceCol);

                  if (targetCol && targetCol.trim() && normalizedSourceCol) {
                    const value = this.findColumnByNormalizedName(
                      esupCompletenessRow,
                      normalizedSourceCol
                    );
                    if (value !== undefined) {
                      const formattedValue = this.formatDateValue(
                        value,
                        columnType
                      );
                      newRow[targetCol] = formattedValue;
                      esupCompletenessFieldsCopied++;
                    }
                  }
                }
              }

              newRow["Таймер обновлен из отчетов ЕСУП"] = "Да";
              
              // Only add row to importData if FSDS match was found
              importData.push(newRow);
            }
            // If no FSDS match found, skip this row entirely
          }
        }
      }

      // Notify frontend that processing is complete
      broadcastProgress({
        type: "progress",
        progress: 100,
        current: pfData.length,
        total: pfData.length,
        message: `Processing complete! Creating Excel file...`,
      });

      // Create worksheet with proper options
      const worksheet = XLSX.utils.json_to_sheet(importData);

      // Set column widths for better readability
      if (allHeaders.length > 0) {
        const colWidths = allHeaders.map(() => ({ wch: 15 }));
        worksheet["!cols"] = colWidths;
      }

      // Ensure we have worksheet range
      const range = XLSX.utils.decode_range(worksheet["!ref"]);

      // Set cell styles for header row (row 0) using xlsx-js-style
      if (allHeaders.length > 0 && range) {
        for (let col = range.s.c; col <= range.e.c; col++) {
          const cellAddress = XLSX.utils.encode_cell({ r: 0, c: col });

          // Ensure the cell exists
          if (worksheet[cellAddress]) {
            // Set cell style using xlsx-js-style format
            worksheet[cellAddress].s = {
              alignment: {
                vertical: "top",
                wrapText: true,
                horizontal: "left",
              },
              font: {
                bold: true,
              },
            };
          }
        }

        // Set freeze panes using xlsx-js-style format
        worksheet["!freeze"] = { xSplit: 0, ySplit: 1 };
      }

      XLSX.utils.book_append_sheet(newWorkbook, worksheet, "import");

      const outputPath = path.join(
        this.uploadsDir,
        process.env.OUTPUT_FILENAME || "import_timers_update.xlsx"
      );

      // Write file with proper options to preserve formatting
      XLSX.writeFile(newWorkbook, outputPath, {
        bookType: "xlsx",
        cellStyles: true,
      });

      // Verify file was created
      if (!fs.existsSync(outputPath)) {
        throw new Error("File was not created");
      }

      console.log(
        `[${new Date().toLocaleString("ru-RU", {
          timeZone: "Europe/Moscow",
        })}] Successfully generated ${
          process.env.OUTPUT_FILENAME || "import_timers_update.xlsx"
        } with ${importData.length} rows`
      );

      return outputPath;
    } catch (error) {
      console.error("Error in createImportFile:", error);
      throw error;
    }
  }

  async processFiles() {
    try {
      this.validateColumnsControl();
      this.validateSourceColumns();
      const outputPath = this.createImportFile();
      return { success: true, filePath: outputPath };
    } catch (error) {
      console.error("ProcessFiles error:", error);
      return { success: false, error: error.message };
    }
  }

  moveUploadsToHistory() {
    try {
      const uploadsDir = this.uploadsDir;
      const historyDir = path.join(
        __dirname,
        process.env.UPLOADS_HISTORY_DIR || "uploads_history"
      );

      // Create history directory if it doesn't exist
      if (!fs.existsSync(historyDir)) {
        fs.mkdirSync(historyDir, { recursive: true });
      }

      // Get current timestamp
      const timestamp = new Date()
        .toISOString()
        .replace(/[:.]/g, "-")
        .slice(0, -5);

      // Read all files in uploads directory
      const files = fs.readdirSync(uploadsDir);

      files.forEach((file) => {
        const sourcePath = path.join(uploadsDir, file);
        const fileExt = path.extname(file);
        const fileName = path.basename(file, fileExt);
        const newFileName = `${fileName}_${timestamp}${fileExt}`;
        const destPath = path.join(historyDir, newFileName);

        try {
          // Move file to history directory with timestamp
          fs.renameSync(sourcePath, destPath);
        } catch (moveError) {
          console.error(`Error moving file ${file}:`, moveError);
        }
      });
    } catch (error) {
      console.error("Error in moveUploadsToHistory:", error);
    }
  }
}

app.post("/upload", upload.single("file"), (req, res) => {
  try {
    if (!req.file) {
      return res
        .status(400)
        .json({ success: false, error: "Файл не был загружен" });
    }

    // Rename file after upload if fileType is provided
    const fileType = req.body.fileType;
    const fileTypeMap = {
      esup_fsds: "esup_fsds",
      esup_completeness: "esup_completeness",
      pf_all_timers: "pf_all_timers",
      columns_control: "columns_control",
    };

    if (fileType && fileTypeMap[fileType]) {
      const originalPath = req.file.path;
      const originalExtension = path.extname(req.file.originalname);
      const newFilename = fileTypeMap[fileType] + originalExtension;
      const newPath = path.join(path.dirname(originalPath), newFilename);

      // Rename the file
      fs.renameSync(originalPath, newPath);

      res.json({
        success: true,
        message: "Файл успешно загружен",
        filename: newFilename,
      });
    } else {
      res.json({
        success: true,
        message: "Файл успешно загружен",
        filename: req.file.filename,
      });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.get("/changelog", (_req, res) => {
  try {
    const changelogPath = path.join(__dirname, "CHANGELOG.md");
    if (fs.existsSync(changelogPath)) {
      const content = fs.readFileSync(changelogPath, "utf8");
      res.json({ success: true, content });
    } else {
      res.status(404).json({ success: false, error: "CHANGELOG.md not found" });
    }
  } catch (error) {
    res.status(500).json({ success: false, error: error.message });
  }
});

app.post("/generate", async (_req, res) => {
  try {
    const processor = new ExcelProcessor();
    const result = await processor.processFiles();

    if (result.success) {
      // Check if file exists and has content
      if (!fs.existsSync(result.filePath)) {
        return res.status(500).json({ error: "Файл не был создан" });
      }

      const stats = fs.statSync(result.filePath);

      if (stats.size < 1000) {
        // File too small, likely empty or corrupted
        return res
          .status(500)
          .json({ error: "Созданный файл поврежден или пуст" });
      }

      // Set proper headers for Excel file download
      res.setHeader(
        "Content-Type",
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
      );
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="${
          process.env.OUTPUT_FILENAME || "import_timers_update.xlsx"
        }"`
      );
      res.setHeader("Content-Length", stats.size);

      // Create read stream and pipe to response
      const fileStream = fs.createReadStream(result.filePath);
      fileStream.pipe(res);

      // Clean up the temporary file and move uploads after stream ends
      fileStream.on("end", () => {
        setTimeout(() => {
          try {
            if (fs.existsSync(result.filePath)) {
              fs.unlinkSync(result.filePath);
            }
            // Move files from uploads to uploads_history with timestamp
            processor.moveUploadsToHistory();
          } catch (cleanupError) {
            console.error("Error cleaning up temp file:", cleanupError);
          }
        }, 1000);
      });
    } else {
      res.status(400).json({ error: result.error });
    }
  } catch (error) {
    console.error("Generate error:", error);
    res
      .status(500)
      .json({ error: "Внутренняя ошибка сервера: " + error.message });
  }
});

server.listen(port, () => {
  console.log(`Сервер запущен на http://localhost:${port}`);
  console.log(`WebSocket сервер готов для подключений`);
  console.log(`Environment: ${process.env.NODE_ENV || "development"}`);
  console.log(`Uploads directory: ${process.env.UPLOADS_DIR || "uploads"}`);
});
